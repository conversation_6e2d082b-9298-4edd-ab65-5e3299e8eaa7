package com.bilibili.common.config.web;

import com.bilibili.common.interceptor.LoginInterceptor;
import com.bilibili.common.interceptor.RefreshTokenInterceptor;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Resource
    private RefreshTokenInterceptor refreshTokenInterceptor;
    @Resource
    private LoginInterceptor loginInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // RefreshTokenInterceptor：用于web模块的token刷新，拦截所有路径
        registry.addInterceptor(refreshTokenInterceptor).addPathPatterns("/**").order(0);

        // LoginInterceptor：用于admin模块的登录验证，只在admin模块生效
        // 通过检查请求URI来判断是否需要执行admin登录验证
        registry.addInterceptor(loginInterceptor).addPathPatterns("/**").order(1);
    }
}
