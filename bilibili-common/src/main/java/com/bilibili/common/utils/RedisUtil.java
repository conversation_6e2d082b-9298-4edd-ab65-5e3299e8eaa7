package com.bilibili.common.utils;

import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component("redisUtils")
public class RedisUtil<V> {

    @Resource
    private RedisTemplate<String, V> redisTemplate;

    private static final Logger logger = LoggerFactory.getLogger(RedisUtil.class);

    /**
     * 删除缓存
     *
     * @param key 可以传一个值 或多个
     */
    public void delete(String... key) {
        if (key != null && key.length > 0) {
            if (key.length == 1) {
                redisTemplate.delete(key[0]);
            } else {
                redisTemplate.delete((Collection<String>) CollectionUtils.arrayToList(key));
            }
        }
    }

    public V get(String key) {
        logger.info("从redis获取key:{}", key);
        V value = key == null ? null : redisTemplate.opsForValue().get(key);
        logger.info("从redis获取value:{}", value);
        return value;
    }

    /**
     * 获取指定类型的对象
     * @param key 键
     * @param clazz 目标类型
     * @return 指定类型的对象
     */
    @SuppressWarnings("unchecked")
    public <T> T get(String key, Class<T> clazz) {
        logger.info("从redis获取key:{}, 目标类型:{}", key, clazz.getSimpleName());
        try {
            V value = get(key);
            if (value == null) {
                return null;
            }

            logger.info("从redis获取到的值类型:{}", value.getClass().getSimpleName());

            // 如果已经是目标类型，直接返回
            if (clazz.isInstance(value)) {
                return (T) value;
            }

            // 由于不使用类型信息，所有复杂对象都会变成 LinkedHashMap，需要转换
            com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
            mapper.configure(com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            return mapper.convertValue(value, clazz);

        } catch (Exception e) {
            logger.error("从redis获取并转换对象失败, key:{}, 目标类型:{}", key, clazz.getSimpleName(), e);
            return null;
        }
    }

    /**
     * 获取指定类型的列表
     * @param key 键
     * @param elementClass 列表元素类型
     * @return 指定类型的列表
     */
    @SuppressWarnings("unchecked")
    public <T> java.util.List<T> getList(String key, Class<T> elementClass) {
        logger.info("从redis获取列表key:{}, 元素类型:{}", key, elementClass.getSimpleName());
        try {
            V value = get(key);
            if (value == null) {
                return null;
            }

            logger.info("从redis获取到的值类型:{}", value.getClass().getSimpleName());

            // 由于不使用类型信息，所有对象都会变成基本类型，List 会保持为 List
            if (value instanceof java.util.List) {
                java.util.List<?> list = (java.util.List<?>) value;
                if (list.isEmpty()) {
                    return new java.util.ArrayList<>();
                }

                // 转换列表中的每个元素
                java.util.List<T> result = new java.util.ArrayList<>();
                com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
                mapper.configure(com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

                for (Object item : list) {
                    if (elementClass.isInstance(item)) {
                        // 如果已经是目标类型，直接添加
                        result.add((T) item);
                    } else {
                        // 否则进行类型转换（通常是 LinkedHashMap -> 目标类型）
                        T convertedItem = mapper.convertValue(item, elementClass);
                        result.add(convertedItem);
                    }
                }
                return result;
            }

            return null;
        } catch (Exception e) {
            logger.error("从redis获取并转换列表失败, key:{}, 元素类型:{}", key, elementClass.getSimpleName(), e);
            return null;
        }
    }

    /**
     * 普通缓存放入
     *
     * @param key   键
     * @param value 值
     * @return true成功 false失败
     */
    public boolean set(String key, V value) {
        try {
            redisTemplate.opsForValue().set(key, value);
            return true;
        } catch (Exception e) {
            logger.error("设置redisKey:{},value:{}失败", key, value);
            return false;
        }
    }

    public boolean keyExists(String key) {
        return redisTemplate.hasKey(key);
    }

    /**
     * 普通缓存放入并设置时间
     *
     * @param key   键
     * @param value 值
     * @param time  时间(秒) time要大于0 如果time小于等于0 将设置无限期
     * @return true成功 false 失败
     */
    public boolean setex(String key, V value, long time) {
        try {
            if (time > 0) {
                redisTemplate.opsForValue().set(key, value, time, TimeUnit.MILLISECONDS);
            } else {
                set(key, value);
            }
            return true;
        } catch (Exception e) {
            logger.error("设置redisKey:{},value:{}失败", key, value);
            return false;
        }
    }

    public boolean expire(String key, long time) {
        try {
            if (time > 0) {
                redisTemplate.expire(key, time, TimeUnit.MILLISECONDS);
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }


    public List<V> getQueueList(String key) {
        return redisTemplate.opsForList().range(key, 0, -1);
    }


    public boolean lpush(String key, V value, Long time) {
        try {
            redisTemplate.opsForList().leftPush(key, value);
            if (time != null && time > 0) {
                expire(key, time);
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    public long remove(String key, Object value) {
        try {
            Long remove = redisTemplate.opsForList().remove(key, 1, value);
            return remove;
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }

    public boolean lpushAll(String key, List<V> values, long time) {
        try {
            redisTemplate.opsForList().leftPushAll(key, values);
            if (time > 0) {
                expire(key, time);
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    public V rpop(String key) {
        try {
            return redisTemplate.opsForList().rightPop(key);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public Long increment(String key) {
        Long count = redisTemplate.opsForValue().increment(key, 1);
        return count;
    }

    public Long incrementex(String key, long milliseconds) {
        Long count = redisTemplate.opsForValue().increment(key, 1);
        if (count == 1) {
            //设置过期时间1天
            expire(key, milliseconds);
        }
        return count;
    }

    public Long decrement(String key) {
        Long count = redisTemplate.opsForValue().increment(key, -1);
        if (count <= 0) {
            redisTemplate.delete(key);
        }
        logger.info("key:{},减少数量{}", key, count);
        return count;
    }


    public Set<String> getByKeyPrefix(String keyPrifix) {
        Set<String> keyList = redisTemplate.keys(keyPrifix + "*");
        return keyList;
    }


    public Map<String, V> getBatch(String keyPrifix) {
        Set<String> keySet = redisTemplate.keys(keyPrifix + "*");
        List<String> keyList = new ArrayList<>(keySet);
        List<V> keyValueList = redisTemplate.opsForValue().multiGet(keyList);
        Map<String, V> resultMap = keyList.stream().collect(Collectors.toMap(key -> key, value -> keyValueList.get(keyList.indexOf(value))));
        return resultMap;
    }

    public void zaddCount(String key, V v) {
        redisTemplate.opsForZSet().incrementScore(key, v, 1);
    }


    public List<V> getZSetList(String key, Integer count) {
        Set<V> topElements = redisTemplate.opsForZSet().reverseRange(key, 0, count);
        List<V> list = new ArrayList<>(topElements);
        return list;
    }

}